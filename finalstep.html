<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
	<title>Final Step - Brawl Stars Free Gems</title>
	<meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=device-dpi">
	<meta name="description" content="Complete the final step to receive your Brawl Stars gems!" />    
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="icon" type="image/ico" href="assets/img/favicon.ico" />
	<!-- Open Graph Meta Tags-->
	<meta property="og:title" content="Final Step - Brawl Stars Free Gems" />
	<meta property="og:description" content="Complete the final step to receive your Brawl Stars gems!" />
	<meta property="og:type" content="website" />
	<meta property="og:url" content="finalstep.html" />
	<!-- Twitter Meta -->
	<meta name="twitter:card" content="summary" />
	<meta name="twitter:title" content="Final Step - Brawl Stars Free Gems" />
	<meta name="twitter:description" content="Complete the final step to receive your Brawl Stars gems!" />
	<!-- Icons -->
	<link rel="stylesheet" href="https://cdn.linearicons.com/free/1.0.0/icon-font.min.css">
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Two+Tone|" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" integrity="sha512-+4zCK9k+qNFUR5X+cKL9EIR+ZOhtIloNl9GIKS57V1MyNsYpYcUrUeQc9vNfzsWfV28IaLL3i96P9sdNyeRssA==" crossorigin="anonymous" />
	<!-- Google Fonts -->
	<link rel="preconnect" href="https://fonts.gstatic.com/">
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&amp;display=swap" rel="stylesheet">
	<!-- CSS -->
	<link href="assets/css/bootstrap.min.css" rel="stylesheet" />  
	<link href="assets/css/animate.min.css" rel="stylesheet" />
	<link href="assets/css/style.css" rel="stylesheet" />	
	<link href="assets/css/a-c-c4.css" rel="stylesheet" />
</head>
<body>	
	<div class="prlp">	
		<div class="prlw">	
			<div class="prlw-r"></div>
			<div class="prlw-l"></div>
		</div>
		<div class="prlc">
			<div class="prlc-i">
				<div class="prlc-l animate__animated animate__bounceIn animation-delay-300"><span class="material-icons-two-tone fa-spin">settings</span></div>
				<div class="prlc-lb animate__animated animate__bounceIn animation-delay-400"><div></div></div>
			</div>
		</div>
	</div>
	
	<header>
		<div class="container">
			<div class="h-c">
				<img src="assets/img/gameicon.png" class="img-fluid l-i" />
				<h1>Complete Last Step</h1>
				<p>Almost There - Final Verification Required</p>
			</div>	
		</div>	
		<div id="header-particles"></div>
	</header>
	
	<section class="m-s">
		<div class="container">
			<div class="c-w c-w-l animate__animated animate__bounceIn">
				<div class="s-o-w">
					<div class="s-o">
						<span class="material-icons-two-tone fa-spin">rotate_right</span>
					</div>
				</div>
				<div class="c-i-t">
					<h4 class="c-w-l-t-v animation-delay-100">Last Step</h4>
					<div class="c-w-l-p-v animation-delay-200">
						Please complete one of the verification steps below to finish the process.
					</div>
				</div>
				
				<!-- Content Locker Boxes -->
				<div class="verification-boxes animation-delay-300">
					<div class="verification-box">
						<iframe src="box1.html" class="verification-iframe" frameborder="0"></iframe>
					</div>
					
					<div class="verification-box">
						<iframe src="box2.html" class="verification-iframe" frameborder="0"></iframe>
					</div>
				</div>
				
				<div class="final-note animation-delay-400">
					<p><strong>Note:</strong> Complete any ONE verification above to receive your gems. This process is completely free and helps us maintain the service for everyone.</p>
				</div>
			</div>
		</div>
	</section>
	
	<div class="bg-o" style="background-image: url('assets/img/WallpaperDog-20526055.jpg');"></div>

	<!-- Custom Styles -->
	<style>
		.verification-boxes {
			display: flex;
			gap: 20px;
			margin: 30px 0;
			flex-wrap: wrap;
		}
		
		.verification-box {
			flex: 1;
			min-width: 350px;
			background: #fff;
			border: 2px solid #e9ecef;
			border-radius: 10px;
			padding: 10px;
			text-align: center;
			transition: all 0.3s ease;
			overflow: hidden;
		}
		
		.verification-box:hover {
			border-color: #9C27B0;
			box-shadow: 0 5px 15px rgba(156, 39, 176, 0.2);
			transform: translateY(-2px);
		}
		
		.verification-iframe {
			width: 100%;
			height: 400px;
			border: none;
			border-radius: 8px;
			background: #f8f9fa;
		}
		
		.final-note {
			background: #e8f5e8;
			border: 1px solid #c3e6c3;
			border-radius: 5px;
			padding: 15px;
			margin-top: 20px;
		}
		
		.final-note p {
			margin: 0;
			color: #2d5a2d;
			font-size: 0.9em;
		}
		
		@media screen and (max-width: 768px) {
			.verification-boxes {
				flex-direction: column;
				gap: 15px;
			}
			
			.verification-box {
				min-width: auto;
			}
			
			.verification-iframe {
				height: 350px;
			}
		}
	</style>

	<!-- JS -->
	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
	<script type="text/javascript" src="assets/js/particles.min.js"></script>
	
	<!-- Initialize particles -->
	<script type="text/javascript">
		$(document).ready(function() {
			// Initialize particles
			if($('#header-particles').length && typeof particlesJS !== 'undefined'){
				particlesJS('header-particles', {
					"particles": {
						"number": {"value": 80, "density": {"enable": true, "value_area": 800}},
						"color": {"value": "#ffffff"},
						"shape": {"type": "circle"},
						"opacity": {"value": 0.5, "random": false},
						"size": {"value": 3, "random": true},
						"line_linked": {"enable": true, "distance": 150, "color": "#ffffff", "opacity": 0.4, "width": 1},
						"move": {"enable": true, "speed": 6, "direction": "none", "random": false, "straight": false, "out_mode": "out", "bounce": false}
					},
					"interactivity": {
						"detect_on": "canvas",
						"events": {"onhover": {"enable": true, "mode": "repulse"}, "onclick": {"enable": true, "mode": "push"}, "resize": true},
						"modes": {"grab": {"distance": 400, "line_linked": {"opacity": 1}}, "bubble": {"distance": 400, "size": 40, "duration": 2, "opacity": 8, "speed": 3}, "repulse": {"distance": 200, "duration": 0.4}, "push": {"particles_nb": 4}, "remove": {"particles_nb": 2}}
					},
					"retina_detect": true
				});
			}
			
			// Hide preloader
			setTimeout(function() {
				$('.prlp').fadeOut();
			}, 1000);
		});
	</script>
</body>
</html>
