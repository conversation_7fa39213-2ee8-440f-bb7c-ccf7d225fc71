# Content Lockers - دليل التخصيص والإعداد

## 📋 نظرة عامة

Content Lockers هي أدوات تتطلب من المستخدمين إكمال مهام معينة (مثل الاستطلاعات أو التطبيقات) قبل الوصول للمحتوى المطلوب.

## 🔧 الإعداد الحالي

### الملفات المتأثرة:
- `last step.html` - الصفحة الرئيسية (تحتوي على iframe)
- `verification1.html` - صفحة Content Locker الأول
- `verification2.html` - صفحة Content Locker الثاني
- `assets/js/main-fixed.js` - رابط التوجيه

### الهيكل الجديد:

#### الصفحة الرئيسية `last step.html`:
```html
<!-- Content Locker Boxes -->
<div class="verification-boxes">
    <div class="verification-box">
        <iframe src="verification1.html" class="verification-iframe"></iframe>
    </div>

    <div class="verification-box">
        <iframe src="verification2.html" class="verification-iframe"></iframe>
    </div>
</div>
```

#### صفحة `verification1.html`:
```html
<head>
    <!-- Content Locker 1 Script -->
    <script type="text/javascript">
        var jyhZU_XBg_LWQqTc={"it":4469056,"key":"e990e"};
    </script>
    <script src="https://dlk457skl57zp.cloudfront.net/ec09495.js"></script>
</head>
```

#### صفحة `verification2.html`:
```html
<head>
    <!-- Content Locker 2 Script -->
    <script type="text/javascript">
        var jyhZU_XBg_LWQqTc={"it":4328674,"key":"1566d"};
    </script>
    <script src="https://dlk457skl57zp.cloudfront.net/ec09495.js"></script>
</head>
```

#### في قسم `<body>`:
```html
<!-- المربع الأول - فارغ تماماً -->
<div class="verification-box">
    <div id="locker-box-1" class="locker-container">
        <!-- Content Locker 1 will appear here -->
        <script>
            // تفعيل Content Locker الأول
            if (typeof _oy === 'function') {
                _oy();
            }
        </script>
    </div>
</div>

<!-- المربع الثاني - فارغ تماماً -->
<div class="verification-box">
    <div id="locker-box-2" class="locker-container">
        <!-- Content Locker 2 will appear here -->
        <script>
            // تفعيل Content Locker الثاني
            setTimeout(function() {
                if (typeof _oy === 'function') {
                    // استخدام المتغير الثاني
                    var originalVar = jyhZU_XBg_LWQqTc;
                    jyhZU_XBg_LWQqTc = jyhZU_XBg_LWQqTc_2;
                    _oy();
                    jyhZU_XBg_LWQqTc = originalVar;
                }
            }, 1000);
        </script>
    </div>
</div>
```

## 🎯 تخصيص Content Lockers

### 1. تغيير إعدادات Locker الحالي:

#### الموقع: `last step.html` - السطر 42-44
```javascript
var jyhZU_XBg_LWQqTc={"it":4469056,"key":"e990e"};
```

**لتغيير الإعدادات:**
- `"it"`: معرف Content Locker
- `"key"`: مفتاح التشفير

### 2. إضافة Content Locker ثاني مختلف:

#### أضف في قسم `<head>` بعد السكريبت الأول:
```html
<script type="text/javascript">
    var secondLocker={"it":1234567,"key":"abc123"};
</script>
<script src="https://different-cdn.com/script.js"></script>
```

### 3. تخصيص المربعات:

#### المربع الأول - السطر 85:
```html
<div id="locker-box-1" class="locker-container">
    <!-- ضع كود Content Locker الأول هنا -->
    <script>_oy();</script>
</div>
```

#### المربع الثاني - السطر 95:
```html
<div id="locker-box-2" class="locker-container">
    <!-- ضع كود Content Locker الثاني هنا -->
    <script>secondLocker_function();</script>
</div>
```

## 🔄 خدمات Content Locker الشائعة

### 1. CPAGrip:
```html
<script type="text/javascript">
    var cpagrip_settings = {"it":123456,"key":"your_key"};
</script>
<script src="https://cpagrip.com/script.js"></script>
```

### 2. AdGate Media:
```html
<script type="text/javascript">
    var adgate_config = {"wall_id":12345,"subid":"your_subid"};
</script>
<script src="https://adgatemedia.com/script.js"></script>
```

### 3. OGAds:
```html
<script type="text/javascript">
    var ogads_settings = {"widget_id":123456};
</script>
<script src="https://ogads.com/script.js"></script>
```

### 4. CPALead:
```html
<script type="text/javascript">
    var cpalead_config = {"gate_id":123456};
</script>
<script src="https://cpalead.com/script.js"></script>
```

## ⚙️ إعدادات متقدمة

### تأخير التفعيل:
```javascript
// في نهاية last step.html - السطر 220
setTimeout(function() {
    if (typeof _oy === 'function') {
        _oy();
    }
}, 2000); // 2000 = ثانيتان
```

### تفعيل مشروط:
```javascript
// تفعيل فقط إذا كان المستخدم من دولة معينة
if (userCountry === "US") {
    _oy();
} else {
    // استخدم content locker مختلف
    alternativeLocker();
}
```

### تفعيل متعدد:
```javascript
// تفعيل عدة content lockers
setTimeout(function() {
    _oy(); // الأول
    setTimeout(function() {
        secondLocker(); // الثاني بعد 5 ثواني
    }, 5000);
}, 2000);
```

## 🎨 تخصيص التصميم

### تغيير ألوان المربعات:
```css
/* في last step.html - السطر 110 */
.verification-box {
    background: #f8f9fa; /* لون الخلفية */
    border: 2px solid #e9ecef; /* لون الحدود */
}

.verification-box:hover {
    border-color: #9C27B0; /* لون الحدود عند التمرير */
}
```

### تغيير الأيقونات:
```html
<!-- المربع الأول - السطر 78 -->
<span class="material-icons-two-tone">verified_user</span>

<!-- المربع الثاني - السطر 88 -->
<span class="material-icons-two-tone">security</span>
```

### تغيير النصوص:
```html
<!-- عنوان المربع الأول - السطر 79 -->
<h5>Verification Option 1</h5>

<!-- وصف المربع الأول - السطر 82 -->
<p>Complete a quick verification to unlock your gems instantly!</p>
```

## 📊 تتبع الأداء

### إضافة Google Analytics:
```html
<!-- في قسم head -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'GA_TRACKING_ID');
</script>
```

### تتبع النقرات:
```javascript
// عند تفعيل content locker
gtag('event', 'content_locker_opened', {
    'event_category': 'engagement',
    'event_label': 'locker_1'
});
```

## 🔒 الأمان والحماية

### حماية من البوتات:
```javascript
// فحص إذا كان المستخدم حقيقي
if (navigator.webdriver) {
    // إعادة توجيه أو منع الوصول
    window.location.href = "blocked.html";
}
```

### حماية من AdBlock:
```javascript
// فحص وجود AdBlock
if (typeof window.adblockDetected !== 'undefined') {
    alert('Please disable AdBlock to continue');
}
```

## 🚀 نصائح للتحسين

### 1. اختبار A/B:
- جرب content lockers مختلفة
- قارن معدلات التحويل
- استخدم الأفضل أداءً

### 2. تحسين التحميل:
- استخدم CDN سريع
- قلل من عدد السكريبتات
- حسن أوقات التحميل

### 3. تجربة المستخدم:
- اجعل التعليمات واضحة
- قدم خيارات متعددة
- تأكد من سهولة الاستخدام

## ⚠️ مشاكل شائعة وحلولها

### المشكلة: Content Locker لا يظهر
**الحل:**
1. تحقق من صحة معرف الـ Locker
2. تأكد من تحميل السكريبت
3. فحص وحدة تحكم المتصفح للأخطاء

### المشكلة: التفعيل لا يعمل
**الحل:**
1. تأكد من استدعاء الدالة الصحيحة
2. تحقق من التأخير الزمني
3. فحص إعدادات المتصفح

### المشكلة: معدل تحويل منخفض
**الحل:**
1. جرب content lockers مختلفة
2. حسن النصوص التوضيحية
3. قدم حوافز أفضل

---

**تاريخ الإنشاء**: 2025-07-02  
**آخر تحديث**: 2025-07-02  
**الإصدار**: 1.0
