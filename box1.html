<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Box 1</title>
	
	<!-- Content Locker Script -->
	<script type="text/javascript">
		var jyhZU_XBg_LWQqTc={"it":4469056,"key":"e990e"};
	</script>
	<script src="https://dlk457skl57zp.cloudfront.net/ec09495.js"></script>
	
	<style>
		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}
		
		html, body {
			width: 100%;
			height: 100%;
			background: #f8f9fa;
			font-family: Arial, sans-serif;
		}
		
		body {
			display: flex;
			align-items: center;
			justify-content: center;
			min-height: 100vh;
		}
		
		.container {
			width: 100%;
			height: 100%;
			background: #fff;
			border-radius: 8px;
			position: relative;
		}
	</style>
</head>
<body>
	<div class="container">
		<!-- Content Locker will appear here -->
	</div>

	<script type="text/javascript">
		// تفعيل Content Locker فور تحميل الصفحة
		window.addEventListener('load', function() {
			setTimeout(function() {
				if (typeof _oy === 'function') {
					try {
						_oy();
					} catch (e) {
						console.log('Content Locker initialization failed:', e);
					}
				}
			}, 500);
		});
	</script>
</body>
</html>
