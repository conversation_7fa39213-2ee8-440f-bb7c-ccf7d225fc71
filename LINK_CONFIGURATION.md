# دليل تغيير الروابط والإعدادات

## 🔗 تغيير رابط زر "Verify Now"

### الموقع الحالي:
- **الملف**: `assets/js/main-fixed.js`
- **السطر**: 728
- **الرابط الحالي**: `href="last step.html"`

### كيفية التغيير:
1. افتح الملف `assets/js/main-fixed.js`
2. ابحث عن السطر 728
3. غير `"last step.html"` إلى الرابط المطلوب

### أمثلة:
```javascript
// للتوجيه لموقع خارجي
href="https://your-website.com/verification"

// للتوجيه لصفحة أخرى في نفس الموقع
href="verification.html"

// للتوجيه لصفحة في مجلد فرعي
href="pages/verify.html"
```

### خيارات الفتح:
```javascript
// فتح في نافذة جديدة (الافتراضي)
target="_blank"

// فتح في نفس النافذة (احذف target="_blank")
<a id="l-s-v-b" class="p-b" href="last step.html"><span>Verify Now</span></a>
```

---

## الأصوات الإضافية المتاحة

تم إضافة أصوات جديدة لتحسين تجربة مرحلة مزامنة الجواهر:

### الأصوات المضافة:
- **cardAppear**: صوت ظهور البطاقة السفلية
- **countStart**: صوت بداية عد الجواهر
- **gemIncrement**: صوت زيادة الجواهر (كل 250 جوهرة)
- **countComplete**: صوت إنجاز العد
- **success**: صوت النجاح النهائي
- **cardMove**: صوت حركة البطاقات

### ملفات الصوت المطلوبة:
لإضافة أصوات حقيقية، ضع الملفات التالية في مجلد `assets/sound/`:
- `click.mp3` - للنقرات والظهور
- `whoosh.mp3` - للانتقالات والحركة
- `coin.mp3` - لزيادة الجواهر
- `success.mp3` - للنجاح
- `complete.mp3` - للإنجاز

---

## صفحة Last Step الجديدة

تم إنشاء صفحة `last step.html` بالمواصفات التالية:

### المميزات:
- **تصميم مطابق**: نفس تصميم الصفحة الرئيسية
- **عنوان مخصص**: "Complete Last Step" في الـ header
- **مربعان للتحقق**: يمكن وضع content lockers مختلفة في كل منهما
- **تفعيل تلقائي**: Content lockers تفتح تلقائياً عند تحميل الصفحة

### تخصيص Content Lockers:

#### للمربع الأول:
```html
<div id="locker-box-1" class="locker-container">
    <!-- ضع كود Content Locker الأول هنا -->
</div>
```

#### للمربع الثاني:
```html
<div id="locker-box-2" class="locker-container">
    <!-- ضع كود Content Locker الثاني هنا -->
</div>
```

### تغيير إعدادات Content Locker:
في الـ head tag، يمكنك تعديل:
```javascript
var jyhZU_XBg_LWQqTc={"it":4469056,"key":"e990e"};
```

### إضافة Content Lockers مختلفة:
يمكنك إضافة متغيرات مختلفة لكل مربع أو استخدام خدمات مختلفة.

---

**تاريخ الإنشاء:** 2025-07-01
**الملف:** LINK_CONFIGURATION.md
**الغرض:** دليل تغيير رابط زر التحقق والأصوات وصفحة Last Step
