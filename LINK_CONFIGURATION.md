# دليل تغيير رابط زر "Verify Now"

## الموقع الحالي للرابط

يمكنك العثور على رابط زر "Verify Now" في الملف التالي:
```
assets/js/main-fixed.js
```

## السطر المحدد

ابحث عن السطر رقم **604** في الملف، والذي يحتوي على:
```javascript
<a id="l-s-v-b" class="p-b" href="https://example.com/verify" target="_blank"><span>Verify Now</span></a>
```

## كيفية تغيير الرابط

1. افتح الملف `assets/js/main-fixed.js`
2. ابحث عن السطر الذي يحتوي على `href="https://example.com/verify"`
3. استبدل `https://example.com/verify` بالرابط الذي تريده

### مثال:
```javascript
// الرابط الحالي
href="https://example.com/verify"

// استبدله بالرابط الجديد
href="https://your-website.com/verification"
```

## خيارات إضافية

### فتح الرابط في نفس النافذة
إذا كنت تريد فتح الرابط في نفس النافذة بدلاً من نافذة جديدة، احذف `target="_blank"`:
```javascript
<a id="l-s-v-b" class="p-b" href="https://your-website.com/verification"><span>Verify Now</span></a>
```

### فتح الرابط في نافذة جديدة (الافتراضي)
```javascript
<a id="l-s-v-b" class="p-b" href="https://your-website.com/verification" target="_blank"><span>Verify Now</span></a>
```

## ملاحظات مهمة

- تأكد من حفظ الملف بعد التعديل
- تأكد من أن الرابط يبدأ بـ `http://` أو `https://`
- يمكنك أيضاً استخدام روابط نسبية مثل `verification.html`

## مثال كامل للتعديل

```javascript
// قبل التعديل
<a id="l-s-v-b" class="p-b" href="https://example.com/verify" target="_blank"><span>Verify Now</span></a>

// بعد التعديل
<a id="l-s-v-b" class="p-b" href="https://mysite.com/complete-verification" target="_blank"><span>Verify Now</span></a>
```

---

## الأصوات الإضافية المتاحة

تم إضافة أصوات جديدة لتحسين تجربة مرحلة مزامنة الجواهر:

### الأصوات المضافة:
- **cardAppear**: صوت ظهور البطاقة السفلية
- **countStart**: صوت بداية عد الجواهر
- **gemIncrement**: صوت زيادة الجواهر (كل 250 جوهرة)
- **countComplete**: صوت إنجاز العد
- **success**: صوت النجاح النهائي
- **cardMove**: صوت حركة البطاقات

### ملفات الصوت المطلوبة:
لإضافة أصوات حقيقية، ضع الملفات التالية في مجلد `assets/sound/`:
- `click.mp3` - للنقرات والظهور
- `whoosh.mp3` - للانتقالات والحركة
- `coin.mp3` - لزيادة الجواهر
- `success.mp3` - للنجاح
- `complete.mp3` - للإنجاز

---

**تاريخ الإنشاء:** 2025-07-01
**الملف:** LINK_CONFIGURATION.md
**الغرض:** دليل تغيير رابط زر التحقق والأصوات
