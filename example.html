<!DOCTYPE html>
<html lang="en">
    <head>
        <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <link rel="apple-touch-icon" sizes="180x180" href="img/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="img/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="img/favicon-16x16.png">
        <link rel="manifest" href="site.webmanifest">
        <link rel="mask-icon" href="safari-pinned-tab.svg" color="#805bd5">
        <meta name="msapplication-TileColor" content="#da532c">
        <meta name="theme-color" content="#ffffff">
        <!-- Vendors CSS -->
        <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
        <link rel="stylesheet" href="css/bootstrap.min.css">
        <!-- Theme styles -->
        <link rel="stylesheet" href="css/main.css">
        <link href="https://fonts.googleapis.com/css?family=Staatliches" rel="stylesheet">
        <title>8 ball pool ! | Free Coins</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    </head>
    
    <style>
            @media all and (max-width:420px) {
                body {
                    background-position: left top;
                    background-size: auto;
                }
            }

            .card {
                background-color: rgba(27, 27, 27, 0.9);

            }
        .container1 {
          width: 100%;
          height: 1000px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
          overflow: hidden;
          margin: 0 auto;
          margin-bottom: 20px; /* added margin bottom for separation */
        }
        iframe {
          width: 100%;
          height: 1100px;
          margin-bottom: -30px; /* Adjusted margin to -30px for spacing */
          border: none;
          overflow: hidden;
        }
        .container2 {
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
  }

  .timer {
    color: black;
    font-size: 30px;
    padding: 5px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid #ddd;
    animation: changeColor 10s infinite alternate; /* 10s duration, infinite repetitions */
  }

  @keyframes changeColor {
    0% {
      background-color: #fff; /* Initial color */
    }
    50% {
      background-color: #ffcc00; /* Intermediate color */
    }
    100% {
      background-color: #ff6666; /* Final color */
    }
  }
      </style>
    
    <body style="background-image: url(img/qw.jpg);"> 
          <div class="container">
            <div class="logo">
                <img src="img/1.png" alt="logo" style="width: 100%; max-width: 680px;">
            </div>
            <br><br>
            
        <br>
        <div class="container2">
            <div class="timer">10:00</div>
          </div>
          
        <br>
        <div class="container1">
          <iframe src="https://fastmod.online/goo/b163543" frameborder="0"></iframe>

        </div>
        <br>
        <script>
            var duration = 600;
  
  var timerDisplay = document.querySelector('.timer');

  var countdown = setInterval(function() {
    var minutes = Math.floor(duration / 60);
    var seconds = duration % 60;

    minutes = (minutes < 10) ? '0' + minutes : minutes;
    seconds = (seconds < 10) ? '0' + seconds : seconds;

    timerDisplay.innerHTML = minutes + ':' + seconds; // Modified this line

    duration--;

    if (duration < 0) {
      clearInterval(countdown);
      timerDisplay.innerHTML = 'Time is up!'; // Modified this line
    }
  }, 1000);
          </script>
        </body>
        </html>