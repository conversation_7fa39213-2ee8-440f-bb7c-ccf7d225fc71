# Brawl Stars Gems Generator - دليل سريع

## 📁 ملفات المشروع

```
brawlstars/
├── index.html              # الصفحة الرئيسية
├── finalstep.html          # صفحة التحقق النهائية
├── box1.html              # Content Locker الأول
├── box2.html              # Content Locker الثاني
├── README.md              # هذا الملف
└── assets/
    ├── css/               # ملفات الأنماط
    ├── img/               # الصور
    ├── js/                # ملفات JavaScript
    └── sound/             # الأصوات
```

## 🎮 تغيير الصور

### صورة أيقونة اللعبة:
- **الملف**: `assets/img/gameicon.png`
- **المواقع**: `index.html` (السطر 57) و `finalstep.html` (السطر 60)

### صورة عملة اللعبة:
- **الملف**: `assets/img/game currency.png`
- **الموقع**: `assets/js/main-fixed.js` (عدة مواضع)

### صورة الخلفية:
- **الملف**: `assets/img/WallpaperDog-20526055.jpg`
- **المرجع**: آخر سطر في `index.html` و `finalstep.html`

## 🔗 تغيير الروابط

### رابط زر "Verify Now":
- **الملف**: `assets/js/main-fixed.js`
- **السطر**: 728
- **القيمة الحالية**: `href="finalstep.html"`

## 🔒 Content Lockers

### تغيير Content Locker الأول:
- **الملف**: `box1.html`
- **السطر**: 10
- **الإعدادات**: `{"it":4469056,"key":"e990e"}`

### تغيير Content Locker الثاني:
- **الملف**: `box2.html`
- **السطر**: 10
- **الإعدادات**: `{"it":4328674,"key":"1566d"}`

## 🔢 تغيير كميات الجواهر

### الكميات المتاحة:
- **الملف**: `assets/js/main-fixed.js`
- **السطور**: 335-354
- **القيم الحالية**: 2500, 5000, 7500, 10000

## 📝 تغيير النصوص

### العنوان الرئيسي:
- **الملف**: `index.html`
- **السطر**: 58

### وصف الموقع:
- **الملف**: `index.html`
- **السطر**: 59

## 🎵 الأصوات

### ملفات الصوت:
- `assets/sound/Button Click.mp3` - صوت النقر
- `assets/sound/coin.mp3` - صوت العملة
- `assets/sound/last step.mp3` - صوت المرحلة الأخيرة
- `assets/sound/transition.mp3` - صوت الانتقال

### تعديل الأصوات:
- **الملف**: `assets/js/main-fixed.js`
- **الدالة**: `playSound()` - السطر 79

## 🚀 نشر الموقع

1. ارفع جميع الملفات إلى الخادم
2. تأكد من صحة مسارات الصور
3. اختبر Content Lockers
4. تأكد من عمل جميع الروابط

## ⚠️ ملاحظات مهمة

- الموقع محسن للعمل بزوم 80%
- لا تحذف ملف `assets/js/main-fixed.js`
- احتفظ بنسخة احتياطية قبل التعديل
- اختبر التغييرات قبل النشر

## 🔧 إضافة Content Locker جديد

1. أنشئ ملف `box3.html`
2. انسخ محتوى `box1.html`
3. غير إعدادات Content Locker
4. أضف iframe جديد في `finalstep.html`

---

**الإصدار**: 2.0  
**تاريخ التحديث**: 2025-07-02
