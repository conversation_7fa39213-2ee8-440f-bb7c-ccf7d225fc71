# Brawl Stars Gems Generator - دليل سريع

## 📁 ملفات المشروع

```
brawlstars/
├── index.html              # الصفحة الرئيسية
├── finalstep.html          # صفحة التحقق النهائية
├── README.md              # هذا الملف
└── assets/
    ├── css/               # ملفات الأنماط
    ├── img/               # الصور
    ├── js/                # ملفات JavaScript
    └── sound/             # الأصوات
```

## 🎮 تغيير الصور

### صورة أيقونة اللعبة:
- **الملف**: `assets/img/gameicon.png`
- **المواقع**: `index.html` (السطر 57) و `finalstep.html` (السطر 60)

### صورة عملة اللعبة:
- **الملف**: `assets/img/game currency.png`
- **الموقع**: `assets/js/main-fixed.js` (عدة مواضع)

### صورة الخلفية:
- **الملف**: `assets/img/WallpaperDog-20526055.jpg`
- **المرجع**: آخر سطر في `index.html` و `finalstep.html`

## 🔗 تغيير الروابط

### رابط زر "Verify Now":
- **الملف**: `assets/js/main-fixed.js`
- **السطر**: 728
- **القيمة الحالية**: `href="finalstep.html"`

## 🔒 Content Lockers

### تغيير Content Locker الأول:
- **الملف**: `finalstep.html`
- **السطر**: 64
- **الرابط الحالي**: `https://fastmod.online/goo/b163543`

### تغيير Content Locker الثاني:
- **الملف**: `finalstep.html`
- **السطر**: 70
- **الرابط الحالي**: `https://fastmod.online/goo/b163544`

### مميزات Content Lockers:
- **حجم مطابق لـ example.html**: عرض 90% بحد أقصى 700px، ارتفاع 950px
- **iframe**: عرض 100%، ارتفاع 1000px مع margin-top: 30px
- **تجاوب للموبايل**: عرض 95% بحد أقصى 400px
- **تصميم مركزي**: محاذاة في وسط الصفحة مع box-shadow

## 🔢 تغيير كميات الجواهر

### الكميات المتاحة:
- **الملف**: `assets/js/main-fixed.js`
- **السطور**: 335-354
- **القيم الحالية**: 2500, 5000, 7500, 10000

## 📝 تغيير النصوص

### العنوان الرئيسي:
- **الملف**: `index.html`
- **السطر**: 58

### وصف الموقع:
- **الملف**: `index.html`
- **السطر**: 59

## 🎵 الأصوات

### ملفات الصوت:
- `assets/sound/Button Click.mp3` - صوت النقر
- `assets/sound/coin.mp3` - صوت العملة
- `assets/sound/last step.mp3` - صوت المرحلة الأخيرة
- `assets/sound/transition.mp3` - صوت الانتقال

### تعديل الأصوات:
- **الملف**: `assets/js/main-fixed.js`
- **الدالة**: `playSound()` - السطر 79


## 🔧 إضافة Content Locker جديد

1. أضف `<div class="verification-box">` جديد في `finalstep.html`
2. أضف `<div class="container1">` بداخله
3. أضف `<iframe>` مع رابط Content Locker الجديد
4. احفظ الملف واختبر التغييرات

---

**الإصدار**: 2.0  
**تاريخ التحديث**: 2025-07-02
