# Brawl Stars Gems Generator - دليل التخصيص

## 📁 هيكل المشروع

```
brawlstars/
├── index.html              # الصفحة الرئيسية
├── last step.html          # صفحة التحقق النهائية
├── README.md              # هذا الملف
├── LINK_CONFIGURATION.md  # دليل تغيير الروابط
├── content locker.md      # دليل Content Lockers
└── assets/
    ├── css/               # ملفات الأنماط
    ├── img/               # الصور
    ├── js/                # ملفات JavaScript
    └── sound/             # الأصوات
```

## 🎮 تغيير صورة اللعبة

### صورة أيقونة اللعبة (في الـ Header):
- **الملف**: `assets/img/gameicon.png`
- **المواقع في الكود**:
  - `index.html` - السطر 57
  - `last step.html` - السطر 60
  - `assets/js/main-fixed.js` - السطر 288

### صورة عملة اللعبة:
- **الملف**: `assets/img/game currency.png`
- **المواقع في الكود**:
  - `assets/js/main-fixed.js` - السطور: 324, 335, 345, 355, 365, 500, 805

## 🎨 تغيير الألوان

### ألوان أشرطة التقدم:
- **البطاقة العلوية**: `assets/js/main-fixed.js` - السطر 750
- **البطاقة السفلية**: `assets/js/main-fixed.js` - السطر 900

### ألوان النصوص المميزة:
- **النصوص المهمة**: `assets/css/style.css` - ابحث عن `.lsv2s`

## 🔗 تغيير الروابط

### رابط زر "Verify Now":
- **الملف**: `assets/js/main-fixed.js`
- **السطر**: 728
- **القيمة الحالية**: `href="last step.html"`

### روابط أخرى:
راجع ملف `LINK_CONFIGURATION.md` للتفاصيل الكاملة.

## 🎵 تغيير الأصوات

### ملفات الصوت المتاحة:
- `assets/sound/Button Click.mp3` - صوت النقر
- `assets/sound/coin.mp3` - صوت العملة
- `assets/sound/last step.mp3` - صوت المرحلة الأخيرة
- `assets/sound/transition.mp3` - صوت الانتقال

### إضافة أصوات جديدة:
1. ضع الملف في مجلد `assets/sound/`
2. عدل دالة `playSound()` في `assets/js/main-fixed.js` - السطر 79

## 📝 تغيير النصوص

### العنوان الرئيسي:
- **الملف**: `index.html`
- **السطر**: 58
- **النص الحالي**: "Brawl Stars Gems Generator"

### وصف الموقع:
- **الملف**: `index.html`
- **السطر**: 59
- **النص الحالي**: "#1 Brawl Stars Free Gems"

### نصوص مرحلة المزامنة:
- **الملف**: `assets/js/main-fixed.js`
- **السطور**: 545-552 (مصفوفة statusTexts)

## 🔢 تغيير كميات الجواهر

### الكميات المتاحة:
- **الملف**: `assets/js/main-fixed.js`
- **السطور**: 335-354
- **القيم الحالية**: 2500, 5000, 7500, 10000

### تغيير القيم:
1. عدل قيم `r-s-i-v` في السطور المذكورة
2. عدل النص في `r-i-s-r-m-v` - السطر 325

## 🖼️ تغيير صورة الخلفية

### الصورة الرئيسية:
- **الملف**: `assets/img/WallpaperDog-20526055.jpg`
- **المرجع في الكود**: 
  - `index.html` - السطر الأخير
  - `last step.html` - السطر الأخير

## ⚙️ إعدادات متقدمة

### تغيير مدة مرحلة المزامنة:
- **الملف**: `assets/js/main-fixed.js`
- **السطر**: 564 (متغير duration)
- **القيمة الحالية**: 6500 (6.5 ثانية)

### تغيير أبعاد البطاقة السفلية:
- **الملف**: `assets/js/main-fixed.js`
- **السطور**: 825-837
- **الأبعاد الحالية**: 250px × 162.09px

## 🔒 Content Lockers

راجع ملف `content locker.md` للتفاصيل الكاملة حول تخصيص Content Lockers.

## 📱 التجاوب

الموقع محسن للعمل على:
- أجهزة الكمبيوتر (بزوم 80% للأفضل)
- الأجهزة اللوحية
- الهواتف المحمولة

## 🚀 نشر الموقع

1. ارفع جميع الملفات إلى الخادم
2. تأكد من أن جميع المسارات صحيحة
3. اختبر جميع الوظائف
4. تأكد من عمل Content Lockers

## ⚠️ ملاحظات مهمة

- لا تحذف ملف `assets/js/main-fixed.js` - يحتوي على جميع الوظائف
- احتفظ بنسخة احتياطية قبل التعديل
- اختبر التغييرات على جهازك أولاً
- تأكد من صحة مسارات الصور بعد التغيير

## 📞 الدعم

إذا واجهت مشاكل، تحقق من:
1. صحة مسارات الملفات
2. عدم وجود أخطاء في وحدة تحكم المتصفح
3. تحميل جميع الملفات بشكل صحيح

---

**تاريخ الإنشاء**: 2025-07-02  
**الإصدار**: 1.0  
**المطور**: Augment Agent
