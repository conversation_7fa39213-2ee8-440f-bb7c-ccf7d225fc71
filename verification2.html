<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
	<title>Verification 2</title>
	<meta name="viewport" content="height=device-height, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=device-dpi">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	
	<!-- Content Locker 2 Script -->
	<script type="text/javascript">
		var jyhZU_XBg_LWQqTc={"it":4328674,"key":"1566d"};
	</script>
	<script src="https://dlk457skl57zp.cloudfront.net/ec09495.js"></script>
	
	<style>
		body {
			margin: 0;
			padding: 20px;
			font-family: 'Roboto', sans-serif;
			background: #f8f9fa;
			min-height: 360px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.verification-container {
			width: 100%;
			max-width: 100%;
			background: #fff;
			border-radius: 8px;
			padding: 20px;
			text-align: center;
			box-shadow: 0 2px 10px rgba(0,0,0,0.1);
			position: relative;
		}
		
		.loading-message {
			color: #6c757d;
			font-size: 0.9em;
			margin: 20px 0;
		}
		
		.verification-title {
			color: #333;
			font-size: 1.1em;
			font-weight: 600;
			margin-bottom: 15px;
		}
		
		.verification-description {
			color: #666;
			font-size: 0.9em;
			margin-bottom: 20px;
		}
		
		.locker-content {
			min-height: 200px;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #f8f9fa;
			border: 1px dashed #dee2e6;
			border-radius: 6px;
			padding: 20px;
			position: relative;
		}
		
		.locker-content:before {
			content: "Loading verification method 2...";
			color: #6c757d;
			font-size: 0.9em;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			z-index: 1;
		}
	</style>
</head>
<body>
	<div class="verification-container">
		<div class="verification-title">Verification Option 2</div>
		<div class="verification-description">Alternative verification method for your convenience</div>
		
		<div id="locker-content-2" class="locker-content">
			<!-- Content Locker 2 will appear here -->
		</div>
		
		<div class="loading-message">Please wait while verification loads...</div>
	</div>

	<!-- Initialize Content Locker -->
	<script type="text/javascript">
		// Wait for page to load completely
		window.addEventListener('load', function() {
			setTimeout(function() {
				// تفعيل Content Locker الثاني
				if (typeof _oy === 'function') {
					try {
						_oy();
						// Hide loading message after locker loads
						setTimeout(function() {
							var loadingMsg = document.querySelector('.loading-message');
							if (loadingMsg) {
								loadingMsg.style.display = 'none';
							}
						}, 2000);
					} catch (e) {
						console.log('Content Locker initialization failed:', e);
					}
				} else {
					console.log('Content Locker function not available');
				}
			}, 1500); // تأخير مختلف لتجنب التداخل
		});
	</script>
</body>
</html>
